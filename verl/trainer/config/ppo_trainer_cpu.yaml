# CPU 版本的 PPO 训练配置
data:
  train_files: ~/data/gsm8k/train.parquet
  val_files: ~/data/gsm8k/test.parquet
  prompt_key: question
  response_key: answer
  train_batch_size: 64  # 减小批次大小以适应 CPU
  val_batch_size: 64
  max_prompt_length: 256
  max_response_length: 512
  truncation: error
  balance_dp_token: False
  chat_template: null

actor_rollout_ref:
  hybrid_engine: False  # 禁用混合引擎
  model:
    path: ~/models/Qwen2.5-0.5B
    external_lib: null
    override_config: {}
    enable_gradient_checkpointing: False
    use_remove_padding: False
  actor:
    strategy: fsdp
    ppo_mini_batch_size: 32  # 减小批次大小
    ppo_micro_batch_size: 8
    use_dynamic_bsz: False
    ppo_max_token_len_per_gpu: 4096  # 减小 token 长度
    grad_clip: 1.0
    clip_ratio: 0.2
    entropy_coeff: 0.001
    use_kl_loss: False
    kl_loss_coef: 0.001
    kl_loss_type: low_var_kl
    ppo_epochs: 1
    shuffle: False
    ulysses_sequence_parallel_size: 1
    optim:
      lr: 1e-6
      lr_warmup_steps_ratio: 0.
      min_lr_ratio: null
      warmup_style: constant
      total_training_steps: -1
    fsdp_config:
      param_offload: True  # 启用参数卸载以节省内存
      grad_offload: True
      optimizer_offload: True
      wrap_policy:
        min_num_params: 0
  rollout:
    name: hf  # 使用 HuggingFace 而不是 VLLM
    temperature: 1.0
    top_k: 50
    top_p: 0.7
    prompt_length: 256
    response_length: 512
    dtype: float32  # 使用 float32 而不是 bfloat16
    micro_batch_size: 8
    log_prob_micro_batch_size: 4
    do_sample: True
    n: 1
  ref:
    log_prob_micro_batch_size: 4

critic:
  strategy: fsdp
  optim:
    lr: 1e-5
  model:
    path: ~/models/Qwen2.5-0.5B
    tokenizer_path: ${actor_rollout_ref.model.path}
    override_config: {}
    external_lib: ${actor_rollout_ref.model.external_lib}
    enable_gradient_checkpointing: False
    use_remove_padding: False
    fsdp_config:
      param_offload: True
      grad_offload: True
      optimizer_offload: True
      wrap_policy:
        min_num_params: 0
  ppo_mini_batch_size: ${actor_rollout_ref.actor.ppo_mini_batch_size}
  ppo_micro_batch_size: 8
  forward_micro_batch_size: ${critic.ppo_micro_batch_size}
  use_dynamic_bsz: ${actor_rollout_ref.actor.use_dynamic_bsz}
  ppo_max_token_len_per_gpu: 8192
  ulysses_sequence_parallel_size: 1

reward_model:
  enable: False

algorithm:
  gamma: 1.0
  lam: 1.0
  adv_estimator: gae
  kl_penalty: kl
  kl_ctrl:
    type: fixed
    kl_coef: 0.001

trainer:
  total_epochs: 5  # 减少训练轮数
  total_training_steps: null
  project_name: TinyZero
  experiment_name: cpu_test
  logger: ['console']  # 只使用控制台日志
  nnodes: 1
  n_gpus_per_node: 0  # 设置为 0 表示不使用 GPU
  save_freq: -1
  test_freq: -1
  critic_warmup: 0
  default_hdfs_dir: null
  default_local_dir: checkpoints/${trainer.project_name}/${trainer.experiment_name}
